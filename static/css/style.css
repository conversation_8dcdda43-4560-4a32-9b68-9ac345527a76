/* ===== MINIMAL ELEGANT THEME ===== */

/* CSS Variables for Minimal Theme */
:root {
  --bg-primary: #fafafa;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f5f5f5;
  --accent-primary: #2563eb;
  --accent-secondary: #64748b;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-light: 1px solid #e2e8f0;
  --border-focus: 1px solid #2563eb;
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.05);
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  line-height: 1.6;
  font-size: 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 { font-size: 2rem; font-weight: 700; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Container */
.container {
  max-width: 1200px;
}

/* Navigation */
.navbar {
  background: var(--bg-secondary) !important;
  border-bottom: var(--border-light);
  box-shadow: var(--shadow-subtle);
  padding: 1rem 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--text-primary) !important;
  text-decoration: none !important;
}

.nav-link {
  color: var(--text-secondary) !important;
  font-weight: 500;
  transition: color 0.2s ease;
  padding: 0.5rem 1rem !important;
  border-radius: var(--radius-sm);
}

.nav-link:hover {
  color: var(--accent-primary) !important;
  background: var(--bg-tertiary);
}

/* Cards */
.card {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-medium);
  transition: all 0.2s ease;
  margin-bottom: 1.5rem;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  background: var(--bg-secondary);
  border-bottom: var(--border-light);
  border-radius: var(--radius-md) var(--radius-md) 0 0 !important;
  padding: 1.25rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
  background: var(--bg-secondary);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-light);
  padding: 1rem 1.5rem;
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

.card-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.card-text {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 0;
}

/* Buttons */
.btn {
  border-radius: var(--radius-sm);
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  line-height: 1.5;
}

.btn-primary {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.btn-primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
  color: white;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-color: var(--border-light);
}

.btn-secondary:hover {
  background: var(--accent-secondary);
  color: white;
  border-color: var(--accent-secondary);
}

.btn-success {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.btn-success:hover {
  background: #059669;
  border-color: #059669;
  color: white;
}

.btn-warning {
  background: #f59e0b;
  color: white;
  border-color: #f59e0b;
}

.btn-warning:hover {
  background: #d97706;
  border-color: #d97706;
  color: white;
}

.btn-danger {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.btn-danger:hover {
  background: #dc2626;
  border-color: #dc2626;
  color: white;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

/* Forms */
.form-control, .form-select {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
  background: var(--bg-secondary);
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  color: var(--text-primary);
  outline: none;
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-label {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Badges */
.badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1.5;
}

.bg-warning {
  background: #f59e0b !important;
  color: white !important;
}

.bg-primary {
  background: var(--accent-primary) !important;
  color: white !important;
}

.bg-success {
  background: #10b981 !important;
  color: white !important;
}

/* Alerts */
.alert {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  border-left: 3px solid var(--accent-primary);
  padding: 1rem;
}

.alert-info {
  border-left-color: var(--accent-primary);
  background: rgba(37, 99, 235, 0.05);
}

.alert-success {
  border-left-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.alert-warning {
  border-left-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.alert-danger {
  border-left-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

/* Modal */
.modal-content {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-medium);
}

.modal-header {
  background: var(--bg-secondary);
  border-bottom: var(--border-light);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.modal-title {
  color: var(--text-primary);
  font-weight: 600;
}

.modal-body {
  color: var(--text-secondary);
}

.modal-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-light);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* Pagination */
.pagination {
  margin-top: 2rem;
}

.page-link {
  background: var(--bg-secondary);
  border: var(--border-light);
  color: var(--text-secondary);
  padding: 0.5rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.page-link:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.page-item.active .page-link {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

/* Special Effects */
.border-danger {
  border-color: #ef4444 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }
}

/* Utility Classes */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* Tables */
.table {
  margin-bottom: 0;
}

.table th {
  background: var(--bg-tertiary);
  border-bottom: var(--border-light);
  font-weight: 600;
  color: var(--text-primary);
  padding: 1rem 0.75rem;
  font-size: 0.875rem;
}

.table td {
  padding: 1rem 0.75rem;
  border-bottom: var(--border-light);
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background: var(--bg-tertiary);
}

.table-responsive {
  border-radius: var(--radius-sm);
  overflow: hidden;
}

/* Admin Cards Styling */
.border-warning {
  border-color: #f59e0b !important;
}

.table tbody tr:has(.badge.bg-warning) {
  background: rgba(245, 158, 11, 0.05);
}

.table tbody tr:has(.badge.bg-warning):hover {
  background: rgba(245, 158, 11, 0.1);
}

/* Mobile Employee Cards */
@media (max-width: 767.98px) {
  .card .card {
    border: var(--border-light);
    box-shadow: var(--shadow-subtle);
  }

  .card .card:hover {
    transform: none;
    box-shadow: var(--shadow-medium);
  }

  .card .card.border-warning {
    background: rgba(245, 158, 11, 0.05);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}