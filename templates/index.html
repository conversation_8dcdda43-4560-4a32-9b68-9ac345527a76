{% extends "base.html" %}

{% block title %}Главная - Dunix WorkFlow{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5">
                <h1 class="mb-3">Панель управления задачами</h1>
                <p class="text-secondary">Простое и эффективное управление задачами</p>
            </div>

            {% if user.is_admin %}
                <!-- Админская панель с таблицей сотрудников -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Сотрудники</h3>
                            <div>
                                <a href="{{ url_for('create_task_route') }}" class="btn btn-primary me-2">
                                    Создать задачу
                                </a>
                                <a href="{{ url_for('all_tasks') }}" class="btn btn-secondary">
                                    Все задачи
                                </a>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <!-- Десктопная версия таблицы -->
                                <div class="d-none d-md-block">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Имя</th>
                                                    <th>Телефон</th>
                                                    <th>Роль</th>
                                                    <th>Активные задачи</th>
                                                    <th>Выполненные задачи</th>
                                                    <th>Действия</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for employee in employees %}
                                                <tr>
                                                    <td>
                                                        <strong>{{ employee.name }}</strong>
                                                    </td>
                                                    <td>{{ employee.phone }}</td>
                                                    <td>
                                                        {% if employee.is_admin %}
                                                            <span class="badge bg-warning">Администратор</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">Сотрудник</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if employee.is_admin %}
                                                            <span class="text-muted">—</span>
                                                        {% else %}
                                                            <span class="badge bg-primary">{{ employee.active_tasks_count }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if employee.is_admin %}
                                                            <span class="text-muted">—</span>
                                                        {% else %}
                                                            <span class="badge bg-success">{{ employee.completed_tasks_count }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if employee.is_admin %}
                                                            <span class="text-muted">Управление</span>
                                                        {% else %}
                                                            <a href="{{ url_for('create_task_route') }}?assignee={{ employee.id }}"
                                                               class="btn btn-sm btn-primary">
                                                                Назначить задачу
                                                            </a>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Мобильная версия карточек -->
                                <div class="d-md-none">
                                    {% for employee in employees %}
                                    <div class="card mb-3 {% if employee.is_admin %}border-warning{% endif %}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">{{ employee.name }}</h6>
                                                {% if employee.is_admin %}
                                                    <span class="badge bg-warning">Администратор</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Сотрудник</span>
                                                {% endif %}
                                            </div>
                                            <p class="text-muted mb-2">{{ employee.phone }}</p>
                                            {% if not employee.is_admin %}
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <div>
                                                    <small class="text-muted">Активные:</small>
                                                    <span class="badge bg-primary ms-1">{{ employee.active_tasks_count }}</span>
                                                </div>
                                                <div>
                                                    <small class="text-muted">Выполненные:</small>
                                                    <span class="badge bg-success ms-1">{{ employee.completed_tasks_count }}</span>
                                                </div>
                                            </div>
                                            <a href="{{ url_for('create_task_route') }}?assignee={{ employee.id }}"
                                               class="btn btn-sm btn-primary w-100">
                                                Назначить задачу
                                            </a>
                                            {% else %}
                                            <div class="text-center py-2">
                                                <span class="text-muted">Управление системой</span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% else %}
                <!-- Обычная панель для сотрудников -->
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Личные задачи</h5>
                                <p class="card-text flex-grow-1">Управляйте своими личными задачами</p>
                                <a href="{{ url_for('create_personal_task_route') }}" class="btn btn-success">
                                    Создать личную задачу
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Мои задачи</h5>
                                <p class="card-text flex-grow-1">Просмотр назначенных вам задач</p>
                                <a href="{{ url_for('my_tasks') }}" class="btn btn-primary">
                                    Мои задачи
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Общие задачи</h5>
                                <p class="card-text flex-grow-1">Просматривайте общедоступные задачи</p>
                                <a href="{{ url_for('public_tasks') }}" class="btn btn-secondary">
                                    Общие задачи
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}